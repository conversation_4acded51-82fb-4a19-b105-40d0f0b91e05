<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Lumora — The Art of Expression</title>
  <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/2913/2913461.png">

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@400;600;700&family=Great+Vibes&display=swap" rel="stylesheet">

  <!-- AOS (Animate On Scroll) -->
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">

  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

  <style>
    :root{
      --beige:#faf5ef;
      --warm-beige:#f7ede2;
      --muted-gold:#d4a373;
      --charcoal:#3e2c2c;
    }

    html { scroll-behavior: smooth; }
    body { background: var(--beige); color: var(--charcoal); font-family: "Inter", system-ui, sans-serif; }

    /* NAVBAR small & centered */
    header { backdrop-filter: blur(6px); }
    .nav-inner { max-width: 1100px; margin: 0 auto; padding: 0.4rem 1rem; display:flex; align-items:center; justify-content:center; position:relative; height:56px; }
    .nav-title { font-family: "Playfair Display", serif; font-weight:600; font-size:1.05rem; display:flex;align-items:center;gap:0.45rem; cursor:pointer; }

    /* Dropdown menu */
    #dropdown-menu { position:absolute; top:56px; left:50%; transform:translateX(-50%); width:220px; border-radius:12px; overflow:hidden;
      box-shadow: 0 6px 24px rgba(62,44,44,0.12); background: rgba(255,255,255,0.96); max-height:0; transition: max-height .42s cubic-bezier(.2,.9,.2,1), opacity .25s ease; opacity:0;
    }
    #dropdown-menu.open { max-height:360px; opacity:1; }

    /* Hero */
    .hero-bg {
      background: linear-gradient(160deg,#f7efe3 0%, #f1dcbf 50%, #e9c78f 100%);
      position:relative; overflow:hidden;
    }
    .shape { position:absolute; pointer-events:none; opacity:0.14; transition: transform .25s linear; filter: blur(0px); }
    .shape-1{ width:140px; top:8%; left:6%; }
    .shape-2{ width:180px; bottom:8%; right:6%; }
    .hero-title { font-family:"Playfair Display", serif; letter-spacing: -1px; }
    @keyframes letterPop {
      0% { letter-spacing: -6px; opacity:0; transform: translateY(18px); }
      60% { opacity:1; transform: translateY(-6px); }
      100% { letter-spacing: 0px; transform: translateY(0); opacity:1; }
    }
    .hero-title.animated { animation: letterPop 1.2s cubic-bezier(.2,.9,.3,1) forwards; }

    /* Floating image in About */
    @keyframes floaty { 0%{ transform: translateY(0)} 50%{ transform: translateY(-8px)} 100%{ transform: translateY(0)} }
    .floaty { animation: floaty 4s ease-in-out infinite; }

    /* highlight animation when arriving to section */
    @keyframes sectionHighlight { 0% { box-shadow: 0 0 0 rgba(212,163,115,0); } 20% { box-shadow: 0 12px 40px rgba(212,163,115,0.12); } 100% { box-shadow: 0 0 0 rgba(212,163,115,0); } }
    .section-highlight { animation: sectionHighlight 1s ease forwards; }

    /* signature */
    .signature { font-family:"Great Vibes", cursive; color: var(--muted-gold); font-size:1.15rem; opacity:.9; }

    /* responsive tweaks */
    @media (min-width:768px){
      .nav-inner{ height:64px; }
      #dropdown-menu { top:64px; }
    }

    /* subtle glass for mobile dropdown overlay */
    .mobile-overlay{ position:fixed; inset:0; background:rgba(14,13,11,0.25); backdrop-filter: blur(3px); z-index:48; display:none; }
    .mobile-overlay.show{ display:block; }
  </style>
</head>
<body>

  <!-- NAVBAR (centered title + dropdown) -->
  <header class="fixed top-0 left-0 right-0 z-50">
    <div class="nav-inner">
      <div id="menu-toggle" class="nav-title text-charcoal">
        <span>Lumora</span>
        <svg id="arrowIcon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[--charcoal]" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="transform:rotate(0deg); transition: transform .28s ease">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <!-- Desktop links (hidden visually on small; kept for accessibility) -->
      <nav class="hidden md:flex absolute right-6 top-1/2 transform -translate-y-1/2 gap-6 text-sm font-medium">
        <a href="#hero" class="hover:text-[--muted-gold] transition">Home</a>
        <a href="#gallery" class="hover:text-[--muted-gold] transition">Gallery</a>
        <a href="#about" class="hover:text-[--muted-gold] transition">About</a>
        <a href="#event" class="hover:text-[--muted-gold] transition">Details</a>
      </nav>
    </div>

    <!-- Dropdown / Mobile menu (centered under title) -->
    <div id="dropdown-menu" aria-hidden="true">
      <div class="flex flex-col text-sm">
        <a href="#hero" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Home</a>
        <a href="#gallery" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Gallery</a>
        <a href="#about" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>About</a>
        <a href="#event" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Details</a>
      </div>
    </div>
  </header>

  <!-- Mobile overlay (dark background when menu open on mobile) -->
  <div id="mobileOverlay" class="mobile-overlay"></div>

  <!-- HERO (full-screen creative) -->
  <main>
    <section id="hero" class="hero-bg min-h-screen flex flex-col justify-center items-center text-center relative pt-20">
      <!-- decorative shapes (parallax) -->
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913461.png" class="shape shape-1" data-speed="3" alt="shape">
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913412.png" class="shape shape-2" data-speed="-2" alt="shape">

      <div class="px-6">
        <h1 class="hero-title text-4xl sm:text-5xl md:text-6xl font-playfair text-[--charcoal] mb-4 opacity-0" data-aos="fade-up">
          The Art of Expression
        </h1>

        <p class="max-w-2xl text-base sm:text-lg text-gray-700 mx-auto opacity-0" data-aos="fade-up" data-aos-delay="300">
          Discover a curated collection of contemporary paintings — where color, form and story meet.
        </p>

        <div class="mt-8 flex items-center justify-center gap-4" data-aos="zoom-in" data-aos-delay="600">
          <a href="#gallery"
             class="inline-flex items-center gap-3 px-6 py-3 rounded-full text-white text-base font-medium
                    bg-gradient-to-r from-amber-500 to-yellow-600 shadow-xl transform transition hover:scale-105 hover:shadow-2xl">
            <i class="fa-solid fa-palette"></i>
            Visit Exhibition
          </a>
          <a href="#about" title="About the exhibition" class="inline-flex items-center justify-center w-12 h-12 rounded-full ring-1 ring-white/40 bg-white/70 hover:bg-white transition">
            <i class="fa-solid fa-info text-[--charcoal]"></i>
          </a>
        </div>
      </div>
    </section>

    <!-- GALLERY -->
    <section id="gallery" class="py-16 px-4 max-w-6xl mx-auto" data-aos="fade-up">
      <h2 class="text-3xl md:text-4xl font-playfair text-center mb-10">Featured Artworks</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <!-- card 1 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="80">
          <img loading="lazy" src="https://picsum.photos/id/1018/900/1200" alt="Artwork 1" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Untitled #1</h3>
              <p class="text-sm opacity-90">Contemporary Artist</p>
            </div>
          </div>
        </div>

        <!-- card 2 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="160">
          <img loading="lazy" src="https://picsum.photos/id/1025/900/1200" alt="Artwork 2" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Color Study</h3>
              <p class="text-sm opacity-90">Emerging Artist</p>
            </div>
          </div>
        </div>

        <!-- card 3 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="240">
          <img loading="lazy" src="https://picsum.photos/id/1035/900/1200" alt="Artwork 3" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Layers</h3>
              <p class="text-sm opacity-90">Studio Series</p>
            </div>
          </div>
        </div>

        <!-- more pieces -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="320">
          <img loading="lazy" src="https://picsum.photos/id/1042/900/1200" alt="Artwork 4" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Drift</h3>
              <p class="text-sm opacity-90">Mixed Media</p>
            </div>
          </div>
        </div>

        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="400">
          <img loading="lazy" src="https://picsum.photos/id/1050/900/1200" alt="Artwork 5" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Gesture</h3>
              <p class="text-sm opacity-90">Minimal Series</p>
            </div>
          </div>
        </div>

        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="480">
          <img loading="lazy" src="https://picsum.photos/id/1062/900/1200" alt="Artwork 6" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Refraction</h3>
              <p class="text-sm opacity-90">Contemporary</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- ABOUT -->
    <section id="about" class="py-16 bg-[--warm-beige]">
      <div class="max-w-6xl mx-auto grid md:grid-cols-2 gap-8 items-center px-6">
        <div data-aos="fade-right">
          <h2 class="text-3xl font-playfair mb-4">About the Exhibition</h2>
          <p class="text-gray-700 leading-relaxed">Lumora brings together contemporary voices in painting — from experimental techniques to refined compositions. This exhibition explores identity, memory and the language of color.</p>
        </div>
        <div class="flex justify-center" data-aos="fade-left">
          <img src="https://picsum.photos/600/420?random=11" alt="illustration" class="rounded-xl shadow-xl floaty w-full max-w-sm object-cover">
        </div>
      </div>
    </section>

    <!-- EVENT DETAILS -->
    <section id="event" class="py-16">
      <div class="max-w-6xl mx-auto px-6">
        <h2 class="text-3xl font-playfair text-center mb-8" data-aos="fade-up">Event Details</h2>
        <div class="grid gap-6 md:grid-cols-3">
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="80">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-regular fa-calendar-days"></i></div>
            <h3 class="font-semibold mb-1">Date</h3>
            <p class="text-gray-600">March 15 — April 20, 2025</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="160">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-location-dot"></i></div>
            <h3 class="font-semibold mb-1">Location</h3>
            <p class="text-gray-600">Lumora Art Hall, Downtown</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="240">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-ticket-simple"></i></div>
            <h3 class="font-semibold mb-1">Tickets</h3>
            <p class="text-gray-600">Available Online & Onsite</p>
          </div>
        </div>
      </div>
    </section>

    <!-- FOOTER -->
    <footer class="bg-[--charcoal] text-white py-8">
      <div class="max-w-6xl mx-auto px-6 flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <div class="text-lg font-playfair">Lumora Art Gallery</div>
          <div class="text-sm opacity-80">© 2025 Lumora — All rights reserved</div>
        </div>
        <div class="signature">Alhassan</div>
        <div class="flex gap-4">
          <a href="#" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-instagram"></i></a>
          <a href="#" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-facebook"></i></a>
          <a href="#" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-twitter"></i></a>
        </div>
      </div>
    </footer>
  </main>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

  <script>
    // init AOS
    AOS.init({ duration: 900, once: true, easing: 'ease-out-cubic' });

    // Dropdown toggle (centered title)
    const menuToggle = document.getElementById('menu-toggle');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const arrowIcon = document.getElementById('arrowIcon');
    const mobileOverlay = document.getElementById('mobileOverlay');

    let open = false;
    function openMenu() {
      dropdownMenu.classList.add('open');
      arrowIcon.style.transform = 'rotate(180deg)';
      mobileOverlay.classList.add('show');
      open = true;
    }
    function closeMenu() {
      dropdownMenu.classList.remove('open');
      arrowIcon.style.transform = 'rotate(0deg)';
      mobileOverlay.classList.remove('show');
      open = false;
    }

    menuToggle.addEventListener('click', (e) => {
      e.stopPropagation();
      open ? closeMenu() : openMenu();
    });

    // close when clicking outside
    document.addEventListener('click', (e) => {
      if (!dropdownMenu.contains(e.target) && !menuToggle.contains(e.target)) closeMenu();
    });

    // links inside dropdown close menu & smooth highlight
    document.querySelectorAll('[data-link]').forEach(link => {
      link.addEventListener('click', (ev) => {
        ev.preventDefault();
        const targetId = link.getAttribute('href') || link.getAttribute('data-href') || '#hero';
        closeMenu();
        // smooth scroll
        const el = document.querySelector(targetId);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start' });
          // add visual highlight
          el.classList.add('section-highlight');
          setTimeout(()=> el.classList.remove('section-highlight'), 1200);
        }
      });
    });

    // Parallax shapes (mousemove)
    document.addEventListener('mousemove', (e) => {
      document.querySelectorAll('.shape').forEach(shape => {
        const speed = parseFloat(shape.dataset.speed) || 1;
        const x = (window.innerWidth - e.pageX * speed) / 100;
        const y = (window.innerHeight - e.pageY * speed) / 100;
        shape.style.transform = `translateX(${x}px) translateY(${y}px)`;
      });
    });

    // Hero title animation trigger after AOS loads to ensure visible
    window.addEventListener('load', () => {
      const title = document.querySelector('.hero-title');
      if (title) {
        setTimeout(()=> title.classList.add('animated'), 150);
      }
    });

    // Smooth anchor links for desktop nav
    document.querySelectorAll('a[href^="#"]').forEach(a=>{
      a.addEventListener('click', function(ev){
        const href = this.getAttribute('href');
        if (!href || href === '#') return;
        const dest = document.querySelector(href);
        if (dest) {
          ev.preventDefault();
          dest.scrollIntoView({ behavior:'smooth', block:'start' });
          dest.classList.add('section-highlight');
          setTimeout(()=> dest.classList.remove('section-highlight'),1200);
          closeMenu();
        }
      });
    });

    // Optional: close menu on ESC
    document.addEventListener('keydown', e => { if (e.key === 'Escape') closeMenu(); });

    // Accessibility: hide dropdown when resizing up
    window.addEventListener('resize', ()=> {
      if (window.innerWidth >= 768) closeMenu();
    });
  </script>
</body>
</html>
