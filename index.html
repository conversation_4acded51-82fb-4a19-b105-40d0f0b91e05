<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=5,user-scalable=yes,viewport-fit=cover" />
  <title>Lu<PERSON>a — The Art of Expression</title>
  <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/2913/2913461.png">

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@400;600;700&family=Great+Vibes&display=swap" rel="stylesheet">

  <!-- AOS (Animate On Scroll) -->
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">

  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

  <style>
    :root{
      --beige:#faf5ef;
      --warm-beige:#f7ede2;
      --muted-gold:#d4a373;
      --charcoal:#3e2c2c;
    }

    html { scroll-behavior: smooth; }
    body { background: var(--beige); color: var(--charcoal); font-family: "Inter", system-ui, sans-serif; }

    /* NAVBAR small & centered */
    header { backdrop-filter: blur(6px); }
    .nav-inner { max-width: 1100px; margin: 0 auto; padding: 0.4rem 1rem; display:flex; align-items:center; justify-content:center; position:relative; height:56px; }
    .nav-title { font-family: "Playfair Display", serif; font-weight:600; font-size:1.05rem; display:flex;align-items:center;gap:0.45rem; cursor:pointer; }

    /* Dropdown menu */
    #dropdown-menu { position:absolute; top:56px; left:50%; transform:translateX(-50%); width:220px; border-radius:12px; overflow:hidden;
      box-shadow: 0 6px 24px rgba(62,44,44,0.12); background: rgba(255,255,255,0.96); max-height:0; transition: max-height .42s cubic-bezier(.2,.9,.2,1), opacity .25s ease; opacity:0;
    }
    #dropdown-menu.open { max-height:360px; opacity:1; }

    /* Hero */
    .hero-bg {
      background: linear-gradient(135deg, #faf5ef 0%, #f7ede2 25%, #e9c78f 50%, #d4a373 75%, #c19a6b 100%);
      position: relative;
      overflow: hidden;
      min-height: 100vh;
    }

    /* Animated background particles */
    .hero-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 30%, rgba(212,163,115,0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 70%, rgba(233,199,143,0.15) 0%, transparent 50%),
                  radial-gradient(circle at 40% 80%, rgba(247,237,226,0.2) 0%, transparent 50%);
      animation: backgroundShift 8s ease-in-out infinite;
    }

    @keyframes backgroundShift {
      0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
      50% { transform: scale(1.1) rotate(2deg); opacity: 0.8; }
    }

    /* Enhanced floating shapes */
    .shape {
      position: absolute;
      pointer-events: none;
      opacity: 0.2;
      transition: all 0.3s ease;
      filter: blur(0.5px);
      animation: floatShape 6s ease-in-out infinite;
    }

    @keyframes floatShape {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-15px) rotate(5deg); }
      66% { transform: translateY(10px) rotate(-3deg); }
    }

    .shape-1 {
      width: 120px;
      top: 10%;
      left: 8%;
      animation-delay: 0s;
      opacity: 0.25;
    }
    .shape-2 {
      width: 160px;
      bottom: 12%;
      right: 8%;
      animation-delay: 2s;
      opacity: 0.2;
    }
    .shape-3 {
      width: 100px;
      top: 60%;
      left: 15%;
      animation-delay: 4s;
      opacity: 0.15;
    }
    .shape-4 {
      width: 80px;
      top: 25%;
      right: 20%;
      animation-delay: 1s;
      opacity: 0.18;
    }

    /* Enhanced hero title with gradient text */
    .hero-title {
      font-family: "Playfair Display", serif;
      letter-spacing: -2px;
      background: linear-gradient(135deg, #3e2c2c 0%, #8b4513 50%, #d4a373 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
    }

    .hero-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, transparent, #d4a373, transparent);
      border-radius: 2px;
      opacity: 0;
      animation: underlineGlow 2s ease-in-out 1.5s forwards;
    }

    @keyframes underlineGlow {
      0% { opacity: 0; width: 0px; }
      100% { opacity: 1; width: 120px; }
    }

    @keyframes letterPop {
      0% {
        letter-spacing: -8px;
        opacity: 0;
        transform: translateY(30px) scale(0.8);
        filter: blur(4px);
      }
      60% {
        opacity: 1;
        transform: translateY(-8px) scale(1.05);
        filter: blur(0px);
      }
      100% {
        letter-spacing: -2px;
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0px);
      }
    }
    .hero-title.animated { animation: letterPop 1.8s cubic-bezier(.2,.9,.3,1) forwards; }

    /* Enhanced subtitle with typewriter effect */
    .hero-subtitle {
      position: relative;
      overflow: hidden;
    }

    .typewriter {
      border-right: 2px solid #d4a373;
      animation: typewriter 3s steps(60) 1s forwards, blink 1s infinite 4s;
      white-space: nowrap;
      overflow: hidden;
      width: 0;
    }

    @keyframes typewriter {
      from { width: 0; }
      to { width: 100%; }
    }

    @keyframes blink {
      0%, 50% { border-color: #d4a373; }
      51%, 100% { border-color: transparent; }
    }

    /* Enhanced buttons with hover effects */
    .hero-btn-primary {
      background: linear-gradient(135deg, #d4a373 0%, #c19a6b 50%, #8b4513 100%);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .hero-btn-primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.6s ease;
    }

    .hero-btn-primary:hover::before {
      left: 100%;
    }

    .hero-btn-secondary {
      backdrop-filter: blur(10px);
      background: rgba(255,255,255,0.8);
      border: 2px solid rgba(212,163,115,0.3);
      transition: all 0.3s ease;
    }

    .hero-btn-secondary:hover {
      background: rgba(212,163,115,0.9);
      border-color: rgba(212,163,115,0.6);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212,163,115,0.3);
    }

    /* Floating image in About */
    @keyframes floaty { 0%{ transform: translateY(0)} 50%{ transform: translateY(-8px)} 100%{ transform: translateY(0)} }
    .floaty { animation: floaty 4s ease-in-out infinite; }

    /* highlight animation when arriving to section */
    @keyframes sectionHighlight { 0% { box-shadow: 0 0 0 rgba(212,163,115,0); } 20% { box-shadow: 0 12px 40px rgba(212,163,115,0.12); } 100% { box-shadow: 0 0 0 rgba(212,163,115,0); } }
    .section-highlight { animation: sectionHighlight 1s ease forwards; }

    /* signature */
    .signature { font-family:"Great Vibes", cursive; color: var(--muted-gold); font-size:1.15rem; opacity:.9; }

    /* Mobile-first responsive design */

    /* Mobile styles (default) */
    @media (max-width: 767px) {
      .hero-bg {
        min-height: 100vh;
        padding: 0 1rem;
      }

      .hero-title {
        font-size: 2.5rem !important;
        line-height: 1.1;
        letter-spacing: -1px;
        margin-bottom: 1.5rem;
      }

      .hero-subtitle .typewriter {
        font-size: 1.1rem !important;
        line-height: 1.4;
        padding: 0 0.5rem;
      }

      .shape {
        opacity: 0.1 !important;
      }

      .shape-1 {
        width: 80px;
        top: 15%;
        left: 5%;
      }
      .shape-2 {
        width: 100px;
        bottom: 20%;
        right: 5%;
      }
      .shape-3 {
        width: 60px;
        top: 65%;
        left: 10%;
      }
      .shape-4 {
        width: 50px;
        top: 30%;
        right: 15%;
      }

      /* Mobile button styles */
      .hero-btn-primary,
      .hero-btn-secondary {
        width: 100%;
        max-width: 280px;
        padding: 1rem 2rem !important;
        font-size: 1rem !important;
        margin: 0.5rem 0;
      }

      /* Disable cursor trail on mobile for performance */
      .cursor-trail-disabled {
        pointer-events: none;
      }

      /* Reduce animations on mobile */
      .shape {
        animation-duration: 8s;
      }

      @keyframes floatShape {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-8px) rotate(2deg); }
      }

      /* Mobile scroll indicator */
      .scroll-indicator {
        bottom: 2rem !important;
      }

      .scroll-indicator span {
        font-size: 0.75rem;
      }

      .scroll-indicator .w-6 {
        width: 1.25rem;
        height: 2rem;
      }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
      .hero-title {
        font-size: 2rem !important;
        margin-bottom: 1rem;
      }

      .hero-subtitle .typewriter {
        font-size: 1rem !important;
      }

      .hero-bg {
        padding: 0 0.75rem;
      }

      .shape-1, .shape-2, .shape-3, .shape-4 {
        display: none; /* Hide shapes on very small screens */
      }

      .hero-btn-primary,
      .hero-btn-secondary {
        padding: 0.875rem 1.5rem !important;
        font-size: 0.9rem !important;
      }
    }

    /* Tablet styles */
    @media (min-width: 768px) and (max-width: 1023px) {
      .nav-inner { height: 64px; }
      #dropdown-menu { top: 64px; }

      .hero-title {
        font-size: 4rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.25rem !important;
      }

      .shape-1 { width: 100px; }
      .shape-2 { width: 130px; }
      .shape-3 { width: 80px; }
      .shape-4 { width: 65px; }
    }

    /* Desktop styles */
    @media (min-width: 1024px) {
      .nav-inner { height: 64px; }
      #dropdown-menu { top: 64px; }

      .hero-title {
        font-size: 5rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.5rem !important;
      }
    }

    /* Large desktop styles */
    @media (min-width: 1280px) {
      .hero-title {
        font-size: 6rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.75rem !important;
      }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
      .hero-btn-primary:hover::before {
        left: 0; /* Disable hover effect on touch devices */
      }

      .hero-btn-secondary:hover {
        transform: none;
        box-shadow: none;
      }

      .shape:hover {
        transform: none !important;
        opacity: inherit !important;
      }
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      .shape,
      .hero-title.animated,
      .typewriter,
      .hero-bg::before {
        animation: none !important;
      }

      .hero-btn-primary,
      .hero-btn-secondary {
        transition: none !important;
      }
    }

    /* subtle glass for mobile dropdown overlay */
    .mobile-overlay{ position:fixed; inset:0; background:rgba(14,13,11,0.25); backdrop-filter: blur(3px); z-index:48; display:none; }
    .mobile-overlay.show{ display:block; }
  </style>
</head>
<body>

  <!-- NAVBAR (centered title + dropdown) -->
  <header class="fixed top-0 left-0 right-0 z-50">
    <div class="nav-inner">
      <div id="menu-toggle" class="nav-title text-charcoal">
        <span>Lumora</span>
        <svg id="arrowIcon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[--charcoal]" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="transform:rotate(0deg); transition: transform .28s ease">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <!-- Desktop links (hidden visually on small; kept for accessibility) -->
      <nav class="hidden md:flex absolute right-6 top-1/2 transform -translate-y-1/2 gap-6 text-sm font-medium">
        <a href="#hero" class="hover:text-[--muted-gold] transition">Home</a>
        <a href="#gallery" class="hover:text-[--muted-gold] transition">Gallery</a>
        <a href="#about" class="hover:text-[--muted-gold] transition">About</a>
        <a href="#event" class="hover:text-[--muted-gold] transition">Details</a>
      </nav>
    </div>

    <!-- Dropdown / Mobile menu (centered under title) -->
    <div id="dropdown-menu" aria-hidden="true">
      <div class="flex flex-col text-sm">
        <a href="#hero" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Home</a>
        <a href="#gallery" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Gallery</a>
        <a href="#about" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>About</a>
        <a href="#event" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Details</a>
      </div>
    </div>
  </header>

  <!-- Mobile overlay (dark background when menu open on mobile) -->
  <div id="mobileOverlay" class="mobile-overlay"></div>

  <!-- HERO (full-screen creative) -->
  <main>
    <section id="hero" class="hero-bg min-h-screen flex flex-col justify-center items-center text-center relative pt-16 sm:pt-20">
      <!-- Enhanced decorative shapes with parallax -->
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913461.png" class="shape shape-1" data-speed="3" alt="decorative shape" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913412.png" class="shape shape-2" data-speed="-2" alt="decorative shape" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913470.png" class="shape shape-3" data-speed="1.5" alt="decorative shape" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2913/2913425.png" class="shape shape-4" data-speed="-1" alt="decorative shape" loading="lazy">

      <!-- Main hero content -->
      <div class="px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-6xl mx-auto">
        <!-- Enhanced title with gradient and animation -->
        <h1 class="hero-title text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-4 sm:mb-6 opacity-0 leading-tight" data-aos="fade-up">
          The Art of Expression
        </h1>

        <!-- Enhanced subtitle with typewriter effect -->
        <div class="hero-subtitle max-w-4xl mx-auto mb-6 sm:mb-8" data-aos="fade-up" data-aos-delay="300">
          <p class="typewriter text-base sm:text-lg md:text-xl lg:text-2xl text-gray-700 font-light leading-relaxed px-2 sm:px-4">
            Discover a curated collection of contemporary paintings — where color, form and story meet in perfect harmony.
          </p>
        </div>

        <!-- Enhanced call-to-action buttons -->
        <div class="mt-8 sm:mt-12 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 px-4" data-aos="zoom-in" data-aos-delay="800">
          <a href="#gallery"
             class="hero-btn-primary w-full sm:w-auto inline-flex items-center justify-center gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-full text-white text-base sm:text-lg font-semibold
                    shadow-2xl transform transition hover:scale-105 hover:shadow-3xl relative overflow-hidden min-w-0">
            <i class="fa-solid fa-palette text-lg sm:text-xl flex-shrink-0"></i>
            <span class="truncate">Explore Exhibition</span>
            <i class="fa-solid fa-arrow-right ml-1 sm:ml-2 transition-transform group-hover:translate-x-1 flex-shrink-0"></i>
          </a>

          <a href="#about"
             class="hero-btn-secondary w-full sm:w-auto inline-flex items-center justify-center gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-full text-gray-700 text-base sm:text-lg font-medium
                    transition-all duration-300 hover:text-white min-w-0">
            <i class="fa-solid fa-info-circle text-lg sm:text-xl flex-shrink-0"></i>
            <span class="truncate">Learn More</span>
          </a>
        </div>

        <!-- Additional decorative elements - hidden on small mobile -->
        <div class="mt-12 sm:mt-16 hidden xs:flex justify-center items-center gap-4 sm:gap-8 opacity-60" data-aos="fade-up" data-aos-delay="1200">
          <div class="w-8 sm:w-16 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
          <div class="flex gap-2 sm:gap-3">
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 animate-pulse"></div>
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-orange-400 to-red-500 animate-pulse" style="animation-delay: 0.5s;"></div>
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-red-400 to-pink-500 animate-pulse" style="animation-delay: 1s;"></div>
          </div>
          <div class="w-8 sm:w-16 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </div>

        <!-- Scroll indicator -->
        <div class="scroll-indicator absolute bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2" data-aos="fade-up" data-aos-delay="1500">
          <div class="flex flex-col items-center gap-1 sm:gap-2 text-gray-500 hover:text-gray-700 transition-colors cursor-pointer" onclick="document.getElementById('gallery').scrollIntoView({behavior: 'smooth'})">
            
            <div class="w-5 h-8 sm:w-6 sm:h-10 border-2 border-gray-400 rounded-full flex justify-center">
              <div class="w-0.5 h-2 sm:w-1 sm:h-3 bg-gray-400 rounded-full mt-1 sm:mt-2 animate-bounce"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- GALLERY -->
    <section id="gallery" class="py-16 px-4 max-w-6xl mx-auto" data-aos="fade-up">
      <h2 class="text-3xl md:text-4xl font-playfair text-center mb-10">Featured Artworks</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <!-- card 1 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="80">
          <img loading="lazy" src="https://picsum.photos/id/1018/900/1200" alt="Artwork 1" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Untitled #1</h3>
              <p class="text-sm opacity-90">Contemporary Artist</p>
            </div>
          </div>
        </div>

        <!-- card 2 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="160">
          <img loading="lazy" src="https://picsum.photos/id/1025/900/1200" alt="Artwork 2" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Color Study</h3>
              <p class="text-sm opacity-90">Emerging Artist</p>
            </div>
          </div>
        </div>

        <!-- card 3 -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="240">
          <img loading="lazy" src="https://picsum.photos/id/1035/900/1200" alt="Artwork 3" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Layers</h3>
              <p class="text-sm opacity-90">Studio Series</p>
            </div>
          </div>
        </div>

        <!-- more pieces -->
        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="320">
          <img loading="lazy" src="https://picsum.photos/id/1042/900/1200" alt="Artwork 4" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Drift</h3>
              <p class="text-sm opacity-90">Mixed Media</p>
            </div>
          </div>
        </div>

        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="400">
          <img loading="lazy" src="https://picsum.photos/id/1050/900/1200" alt="Artwork 5" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Gesture</h3>
              <p class="text-sm opacity-90">Minimal Series</p>
            </div>
          </div>
        </div>

        <div class="relative rounded-xl overflow-hidden shadow-lg group" data-aos="zoom-in" data-aos-delay="480">
          <img loading="lazy" src="https://picsum.photos/id/1062/900/1200" alt="Artwork 6" class="w-full h-72 object-cover transform transition group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition flex items-end p-4">
            <div class="text-white">
              <h3 class="font-semibold">Refraction</h3>
              <p class="text-sm opacity-90">Contemporary</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- ABOUT -->
    <section id="about" class="py-16 bg-[--warm-beige]">
      <div class="max-w-6xl mx-auto grid md:grid-cols-2 gap-8 items-center px-6">
        <div data-aos="fade-right">
          <h2 class="text-3xl font-playfair mb-4">About the Exhibition</h2>
          <p class="text-gray-700 leading-relaxed">Lumora brings together contemporary voices in painting — from experimental techniques to refined compositions. This exhibition explores identity, memory and the language of color.</p>
        </div>
        <div class="flex justify-center" data-aos="fade-left">
          <img src="https://picsum.photos/600/420?random=11" alt="illustration" class="rounded-xl shadow-xl floaty w-full max-w-sm object-cover">
        </div>
      </div>
    </section>

    <!-- EVENT DETAILS -->
    <section id="event" class="py-16">
      <div class="max-w-6xl mx-auto px-6">
        <h2 class="text-3xl font-playfair text-center mb-8" data-aos="fade-up">Event Details</h2>
        <div class="grid gap-6 md:grid-cols-3">
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="80">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-regular fa-calendar-days"></i></div>
            <h3 class="font-semibold mb-1">Date</h3>
            <p class="text-gray-600">March 15 — April 20, 2025</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="160">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-location-dot"></i></div>
            <h3 class="font-semibold mb-1">Location</h3>
            <p class="text-gray-600">Lumora Art Hall, Downtown</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="240">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-ticket-simple"></i></div>
            <h3 class="font-semibold mb-1">Tickets</h3>
            <p class="text-gray-600">Available Online & Onsite</p>
          </div>
        </div>
      </div>
    </section>

    <!-- FOOTER -->
    <footer class="bg-[--charcoal] text-white py-8">
      <div class="max-w-6xl mx-auto px-6 flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <div class="text-lg font-playfair">Lumora Art Gallery</div>
          <div class="text-sm opacity-80">© 2025 Lumora — All rights reserved</div>
        </div>
        <div class="signature">Alhassan</div>
        <div class="flex gap-4">
          <a href="#" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-instagram"></i></a>
          <a href="#" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-facebook"></i></a>
         
        </div>
      </div>
    </footer>
  </main>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

  <script>
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // init AOS with mobile-optimized settings
    AOS.init({
      duration: isMobile ? 600 : 900,
      once: true,
      easing: 'ease-out-cubic',
      offset: isMobile ? 50 : 120,
      disable: function() {
        return window.innerWidth < 480; // Disable on very small screens
      }
    });

    // Dropdown toggle (centered title)
    const menuToggle = document.getElementById('menu-toggle');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const arrowIcon = document.getElementById('arrowIcon');
    const mobileOverlay = document.getElementById('mobileOverlay');

    let open = false;
    function openMenu() {
      dropdownMenu.classList.add('open');
      arrowIcon.style.transform = 'rotate(180deg)';
      mobileOverlay.classList.add('show');
      open = true;
    }
    function closeMenu() {
      dropdownMenu.classList.remove('open');
      arrowIcon.style.transform = 'rotate(0deg)';
      mobileOverlay.classList.remove('show');
      open = false;
    }

    menuToggle.addEventListener('click', (e) => {
      e.stopPropagation();
      open ? closeMenu() : openMenu();
    });

    // close when clicking outside
    document.addEventListener('click', (e) => {
      if (!dropdownMenu.contains(e.target) && !menuToggle.contains(e.target)) closeMenu();
    });

    // links inside dropdown close menu & smooth highlight
    document.querySelectorAll('[data-link]').forEach(link => {
      link.addEventListener('click', (ev) => {
        ev.preventDefault();
        const targetId = link.getAttribute('href') || link.getAttribute('data-href') || '#hero';
        closeMenu();
        // smooth scroll
        const el = document.querySelector(targetId);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start' });
          // add visual highlight
          el.classList.add('section-highlight');
          setTimeout(()=> el.classList.remove('section-highlight'), 1200);
        }
      });
    });

    // Enhanced parallax shapes with mobile optimization
    let mouseX = 0, mouseY = 0;
    let targetX = 0, targetY = 0;
    let isAnimating = false;

    // Only enable parallax on desktop
    if (!isMobile && !isTouch) {
      document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
      });

      // Smooth parallax animation
      function animateParallax() {
        if (!isAnimating) return;

        targetX += (mouseX - targetX) * 0.05;
        targetY += (mouseY - targetY) * 0.05;

        document.querySelectorAll('.shape').forEach(shape => {
          const speed = parseFloat(shape.dataset.speed) || 1;
          const x = (targetX - window.innerWidth / 2) * speed * 0.01;
          const y = (targetY - window.innerHeight / 2) * speed * 0.01;

          const currentTransform = shape.style.transform;
          const scaleMatch = currentTransform.match(/scale\([^)]*\)/);
          const scaleTransform = scaleMatch ? scaleMatch[0] : '';

          shape.style.transform = `translateX(${x}px) translateY(${y}px) ${scaleTransform}`;
        });

        requestAnimationFrame(animateParallax);
      }

      // Start parallax animation only on desktop
      isAnimating = true;
      animateParallax();

      // Add cursor trail effect for hero section (desktop only)
      const heroSection = document.getElementById('hero');
      let trail = [];
      let lastTrailTime = 0;

      heroSection.addEventListener('mousemove', (e) => {
        const now = Date.now();
        if (now - lastTrailTime < 50) return; // Throttle trail creation
        lastTrailTime = now;

        const rect = heroSection.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Create trail dot
        const dot = document.createElement('div');
        dot.style.cssText = `
          position: absolute;
          left: ${x}px;
          top: ${y}px;
          width: 4px;
          height: 4px;
          background: radial-gradient(circle, rgba(212,163,115,0.6) 0%, transparent 70%);
          border-radius: 50%;
          pointer-events: none;
          z-index: 5;
          animation: trailFade 1s ease-out forwards;
        `;

        heroSection.appendChild(dot);
        trail.push(dot);

        // Remove old trail dots
        if (trail.length > 15) {
          const oldDot = trail.shift();
          if (oldDot && oldDot.parentNode) {
            oldDot.parentNode.removeChild(oldDot);
          }
        }

        // Remove dot after animation
        setTimeout(() => {
          if (dot && dot.parentNode) {
            dot.parentNode.removeChild(dot);
          }
        }, 1000);
      });

      // Add trail fade animation
      const style = document.createElement('style');
      style.textContent = `
        @keyframes trailFade {
          0% { opacity: 1; transform: scale(1); }
          100% { opacity: 0; transform: scale(0.3); }
        }
      `;
      document.head.appendChild(style);
    }

    // Mobile-specific optimizations
    if (isMobile || isTouch) {
      // Disable expensive animations on mobile
      document.querySelectorAll('.shape').forEach(shape => {
        shape.style.willChange = 'auto';
      });

      // Add touch feedback for buttons
      document.querySelectorAll('.hero-btn-primary, .hero-btn-secondary').forEach(btn => {
        btn.addEventListener('touchstart', function() {
          this.style.transform = 'scale(0.95)';
        });

        btn.addEventListener('touchend', function() {
          this.style.transform = 'scale(1)';
        });
      });
    }

    // Enhanced hero animations with mobile optimization
    window.addEventListener('load', () => {
      const title = document.querySelector('.hero-title');
      const typewriter = document.querySelector('.typewriter');

      if (title) {
        setTimeout(() => {
          title.classList.add('animated');
          title.style.opacity = '1';
        }, isMobile ? 150 : 300);
      }

      // Start typewriter effect after title animation (faster on mobile)
      if (typewriter) {
        const typewriterDelay = isMobile ? 800 : 1500;
        const typewriterDuration = isMobile ? 2 : 3;
        const steps = isMobile ? 40 : 60;

        setTimeout(() => {
          typewriter.style.animation = `typewriter ${typewriterDuration}s steps(${steps}) forwards, blink 1s infinite ${typewriterDuration}s`;
        }, typewriterDelay);
      }

      // Add floating animation to shapes on hover (desktop only)
      if (!isMobile && !isTouch) {
        document.querySelectorAll('.shape').forEach(shape => {
          shape.addEventListener('mouseenter', () => {
            shape.style.transform += ' scale(1.1)';
            shape.style.opacity = '0.4';
          });

          shape.addEventListener('mouseleave', () => {
            shape.style.transform = shape.style.transform.replace(' scale(1.1)', '');
            shape.style.opacity = shape.classList.contains('shape-1') ? '0.25' :
                                  shape.classList.contains('shape-2') ? '0.2' :
                                  shape.classList.contains('shape-3') ? '0.15' : '0.18';
          });
        });
      }
    });

    // Smooth anchor links for desktop nav
    document.querySelectorAll('a[href^="#"]').forEach(a=>{
      a.addEventListener('click', function(ev){
        const href = this.getAttribute('href');
        if (!href || href === '#') return;
        const dest = document.querySelector(href);
        if (dest) {
          ev.preventDefault();
          dest.scrollIntoView({ behavior:'smooth', block:'start' });
          dest.classList.add('section-highlight');
          setTimeout(()=> dest.classList.remove('section-highlight'),1200);
          closeMenu();
        }
      });
    });

    // Optional: close menu on ESC
    document.addEventListener('keydown', e => { if (e.key === 'Escape') closeMenu(); });

    // Accessibility: hide dropdown when resizing up
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (window.innerWidth >= 768) closeMenu();

        // Update mobile detection on resize
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
          location.reload(); // Reload to apply mobile optimizations
        }
      }, 250);
    });

    // Performance optimizations for mobile
    if (isMobile || isTouch) {
      // Reduce animation frequency
      document.documentElement.style.setProperty('--animation-duration', '4s');

      // Optimize scroll performance
      let ticking = false;
      function updateScrollEffects() {
        // Minimal scroll effects for mobile
        ticking = false;
      }

      window.addEventListener('scroll', () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollEffects);
          ticking = true;
        }
      }, { passive: true });

      // Prevent zoom on double tap for buttons
      document.querySelectorAll('.hero-btn-primary, .hero-btn-secondary').forEach(btn => {
        btn.addEventListener('touchend', function(e) {
          e.preventDefault();
          this.click();
        });
      });
    }

    // Preload critical images for better performance
    const criticalImages = [
      'https://cdn-icons-png.flaticon.com/512/2913/2913461.png',
      'https://cdn-icons-png.flaticon.com/512/2913/2913412.png'
    ];

    criticalImages.forEach(src => {
      const img = new Image();
      img.src = src;
    });

    // Intersection Observer for performance
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      });

      // Observe lazy-loaded images
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  </script>
</body>
</html>
