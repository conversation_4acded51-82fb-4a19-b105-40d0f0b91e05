<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=5,user-scalable=yes,viewport-fit=cover" />
  <title>Lu<PERSON>a — The Art of Expression</title>
  <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/2913/2913461.png">

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@400;600;700&family=Great+Vibes&display=swap" rel="stylesheet">

  <!-- AOS (Animate On Scroll) -->
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">

  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

  <style>
    :root{
      --beige:#faf5ef;
      --warm-beige:#f7ede2;
      --muted-gold:#d4a373;
      --charcoal:#3e2c2c;
    }

    html { scroll-behavior: smooth; }
    body { background: var(--beige); color: var(--charcoal); font-family: "Inter", system-ui, sans-serif; }

    /* NAVBAR small & centered */
    header { backdrop-filter: blur(6px); }
    .nav-inner { max-width: 1100px; margin: 0 auto; padding: 0.4rem 1rem; display:flex; align-items:center; justify-content:center; position:relative; height:56px; }
    .nav-title { font-family: "Playfair Display", serif; font-weight:600; font-size:1.05rem; display:flex;align-items:center;gap:0.45rem; cursor:pointer; }

    /* Dropdown menu */
    #dropdown-menu { position:absolute; top:56px; left:50%; transform:translateX(-50%); width:220px; border-radius:12px; overflow:hidden;
      box-shadow: 0 6px 24px rgba(62,44,44,0.12); background: rgba(255,255,255,0.96); max-height:0; transition: max-height .42s cubic-bezier(.2,.9,.2,1), opacity .25s ease; opacity:0;
    }
    #dropdown-menu.open { max-height:360px; opacity:1; }

    /* Hero */
    .hero-bg {
      background: linear-gradient(135deg, #faf5ef 0%, #f7ede2 25%, #e9c78f 50%, #d4a373 75%, #c19a6b 100%);
      position: relative;
      overflow: hidden;
      min-height: 100vh;
    }

    /* Animated background particles */
    .hero-bg::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 30%, rgba(212,163,115,0.1) 0%, transparent 50%),
                  radial-gradient(circle at 80% 70%, rgba(233,199,143,0.15) 0%, transparent 50%),
                  radial-gradient(circle at 40% 80%, rgba(247,237,226,0.2) 0%, transparent 50%);
      animation: backgroundShift 8s ease-in-out infinite;
    }

    @keyframes backgroundShift {
      0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.6; }
      50% { transform: scale(1.1) rotate(2deg); opacity: 0.8; }
    }

    /* Enhanced floating shapes */
    .shape {
      position: absolute;
      pointer-events: none;
      opacity: 0.2;
      transition: all 0.3s ease;
      filter: blur(0.5px);
      animation: floatShape 6s ease-in-out infinite;
    }

    @keyframes floatShape {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      33% { transform: translateY(-15px) rotate(5deg); }
      66% { transform: translateY(10px) rotate(-3deg); }
    }

    .shape-1 {
      width: 120px;
      top: 10%;
      left: 8%;
      animation-delay: 0s;
      opacity: 0.25;
    }
    .shape-2 {
      width: 160px;
      bottom: 12%;
      right: 8%;
      animation-delay: 2s;
      opacity: 0.2;
    }
    .shape-3 {
      width: 100px;
      top: 60%;
      left: 15%;
      animation-delay: 4s;
      opacity: 0.15;
    }
    .shape-4 {
      width: 80px;
      top: 25%;
      right: 20%;
      animation-delay: 1s;
      opacity: 0.18;
    }
    .shape-5 {
      width: 70px;
      top: 45%;
      right: 5%;
      animation-delay: 3s;
      opacity: 0.16;
    }
    .shape-6 {
      width: 110px;
      bottom: 35%;
      left: 5%;
      animation-delay: 5s;
      opacity: 0.22;
    }

    /* Enhanced hero title with gradient text */
    .hero-title {
      font-family: "Playfair Display", serif;
      letter-spacing: -2px;
      background: linear-gradient(135deg, #3e2c2c 0%, #8b4513 50%, #d4a373 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      position: relative;
    }

    .hero-title::after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 3px;
      background: linear-gradient(90deg, transparent, #d4a373, transparent);
      border-radius: 2px;
      opacity: 0;
      animation: underlineGlow 2s ease-in-out 1.5s forwards;
    }

    @keyframes underlineGlow {
      0% { opacity: 0; width: 0px; }
      100% { opacity: 1; width: 120px; }
    }

    @keyframes letterPop {
      0% {
        letter-spacing: -8px;
        opacity: 0;
        transform: translateY(30px) scale(0.8);
        filter: blur(4px);
      }
      60% {
        opacity: 1;
        transform: translateY(-8px) scale(1.05);
        filter: blur(0px);
      }
      100% {
        letter-spacing: -2px;
        transform: translateY(0) scale(1);
        opacity: 1;
        filter: blur(0px);
      }
    }
    .hero-title.animated { animation: letterPop 1.8s cubic-bezier(.2,.9,.3,1) forwards; }

    /* Enhanced subtitle with typewriter effect */
    .hero-subtitle {
      position: relative;
      overflow: hidden;
    }

    .typewriter {
      border-right: 2px solid #d4a373;
      animation: typewriter 3s steps(60) 1s forwards, blink 1s infinite 4s;
      white-space: nowrap;
      overflow: hidden;
      width: 0;
    }

    @keyframes typewriter {
      from { width: 0; }
      to { width: 100%; }
    }

    @keyframes blink {
      0%, 50% { border-color: #d4a373; }
      51%, 100% { border-color: transparent; }
    }

    /* Enhanced buttons with hover effects */
    .hero-btn-primary {
      background: linear-gradient(135deg, #d4a373 0%, #c19a6b 50%, #8b4513 100%);
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .hero-btn-primary::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.6s ease;
    }

    .hero-btn-primary:hover::before {
      left: 100%;
    }

    .hero-btn-secondary {
      backdrop-filter: blur(10px);
      background: rgba(255,255,255,0.8);
      border: 2px solid rgba(212,163,115,0.3);
      transition: all 0.3s ease;
    }

    .hero-btn-secondary:hover {
      background: rgba(212,163,115,0.9);
      border-color: rgba(212,163,115,0.6);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212,163,115,0.3);
    }

    /* Floating image in About */
    @keyframes floaty { 0%{ transform: translateY(0)} 50%{ transform: translateY(-8px)} 100%{ transform: translateY(0)} }
    .floaty { animation: floaty 4s ease-in-out infinite; }

    /* highlight animation when arriving to section */
    @keyframes sectionHighlight { 0% { box-shadow: 0 0 0 rgba(212,163,115,0); } 20% { box-shadow: 0 12px 40px rgba(212,163,115,0.12); } 100% { box-shadow: 0 0 0 rgba(212,163,115,0); } }
    .section-highlight { animation: sectionHighlight 1s ease forwards; }

    /* signature */
    .signature { font-family:"Great Vibes", cursive; color: var(--muted-gold); font-size:1.15rem; opacity:.9; }

    /* Mobile-first responsive design */

    /* Mobile styles (default) */
    @media (max-width: 767px) {
      .hero-bg {
        min-height: 100vh;
        padding: 0 1rem;
      }

      .hero-title {
        font-size: 2.5rem !important;
        line-height: 1.1;
        letter-spacing: -1px;
        margin-bottom: 1.5rem;
      }

      .hero-subtitle .typewriter {
        font-size: 1.1rem !important;
        line-height: 1.4;
        padding: 0 0.5rem;
      }

      .shape {
        opacity: 0.1 !important;
      }

      .shape-1 {
        width: 80px;
        top: 15%;
        left: 5%;
      }
      .shape-2 {
        width: 100px;
        bottom: 20%;
        right: 5%;
      }
      .shape-3 {
        width: 60px;
        top: 65%;
        left: 10%;
      }
      .shape-4 {
        width: 50px;
        top: 30%;
        right: 15%;
      }
      .shape-5 {
        width: 45px;
        top: 50%;
        right: 8%;
      }
      .shape-6 {
        width: 70px;
        bottom: 40%;
        left: 8%;
      }

      /* Mobile button styles */
      .hero-btn-primary,
      .hero-btn-secondary {
        width: 100%;
        max-width: 280px;
        padding: 1rem 2rem !important;
        font-size: 1rem !important;
        margin: 0.5rem 0;
      }

      /* Disable cursor trail on mobile for performance */
      .cursor-trail-disabled {
        pointer-events: none;
      }

      /* Reduce animations on mobile */
      .shape {
        animation-duration: 8s;
      }

      @keyframes floatShape {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-8px) rotate(2deg); }
      }

      /* Mobile scroll indicator */
      .scroll-indicator {
        bottom: 2rem !important;
      }

      .scroll-indicator span {
        font-size: 0.75rem;
      }

      .scroll-indicator .w-6 {
        width: 1.25rem;
        height: 2rem;
      }
    }

    /* Small mobile devices */
    @media (max-width: 480px) {
      .hero-title {
        font-size: 2rem !important;
        margin-bottom: 1rem;
      }

      .hero-subtitle .typewriter {
        font-size: 1rem !important;
      }

      .hero-bg {
        padding: 0 0.75rem;
      }

      .shape-1, .shape-2, .shape-3, .shape-4, .shape-5, .shape-6 {
        display: none; /* Hide shapes on very small screens */
      }

      .hero-btn-primary,
      .hero-btn-secondary {
        padding: 0.875rem 1.5rem !important;
        font-size: 0.9rem !important;
      }
    }

    /* Tablet styles */
    @media (min-width: 768px) and (max-width: 1023px) {
      .nav-inner { height: 64px; }
      #dropdown-menu { top: 64px; }

      .hero-title {
        font-size: 4rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.25rem !important;
      }

      .shape-1 { width: 100px; }
      .shape-2 { width: 130px; }
      .shape-3 { width: 80px; }
      .shape-4 { width: 65px; }
      .shape-5 { width: 60px; }
      .shape-6 { width: 90px; }
    }

    /* Desktop styles */
    @media (min-width: 1024px) {
      .nav-inner { height: 64px; }
      #dropdown-menu { top: 64px; }

      .hero-title {
        font-size: 5rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.5rem !important;
      }
    }

    /* Large desktop styles */
    @media (min-width: 1280px) {
      .hero-title {
        font-size: 6rem !important;
      }

      .hero-subtitle .typewriter {
        font-size: 1.75rem !important;
      }
    }

    /* Touch device optimizations */
    @media (hover: none) and (pointer: coarse) {
      .hero-btn-primary:hover::before {
        left: 0; /* Disable hover effect on touch devices */
      }

      .hero-btn-secondary:hover {
        transform: none;
        box-shadow: none;
      }

      .shape:hover {
        transform: none !important;
        opacity: inherit !important;
      }
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      .shape,
      .hero-title.animated,
      .typewriter,
      .hero-bg::before {
        animation: none !important;
      }

      .hero-btn-primary,
      .hero-btn-secondary {
        transition: none !important;
      }
    }

    /* Enhanced Gallery Modal with Purchase Option */
    .gallery-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      backdrop-filter: blur(10px);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      padding: 20px;
    }

    .gallery-modal.active {
      opacity: 1;
      visibility: visible;
    }

    .modal-content {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      background: white;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
      transform: scale(0.8);
      transition: transform 0.3s ease;
      display: flex;
      flex-direction: column;
    }

    .gallery-modal.active .modal-content {
      transform: scale(1);
    }

    .modal-image {
      width: 100%;
      height: 70vh;
      object-fit: contain;
      background: #f8f8f8;
      display: block;
    }

    .modal-info {
      padding: 20px;
      background: linear-gradient(135deg, #faf5ef 0%, #f7ede2 100%);
      display: flex;
      justify-content: space-between;
      align-items: center;
      min-height: 80px;
    }

    .artwork-details h3 {
      font-family: "Playfair Display", serif;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--charcoal);
      margin-bottom: 5px;
    }

    .artwork-price {
      font-size: 1.25rem;
      font-weight: bold;
      color: var(--muted-gold);
    }

    .buy-button {
      background: linear-gradient(135deg, #d4a373 0%, #c19a6b 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .buy-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(212, 163, 115, 0.4);
    }

    .close-modal {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 45px;
      height: 45px;
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1.3rem;
      color: var(--charcoal);
      transition: all 0.3s ease;
      z-index: 10;
    }

    .close-modal:hover {
      background: white;
      transform: scale(1.1);
    }

    .nav-arrows {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
      height: 50px;
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 1.5rem;
      color: var(--charcoal);
      transition: all 0.3s ease;
      z-index: 10;
    }

    .nav-arrows:hover {
      background: white;
      transform: translateY(-50%) scale(1.1);
    }

    .prev-arrow {
      left: 20px;
    }

    .next-arrow {
      right: 20px;
    }

    /* Mobile responsive for modal */
    @media (max-width: 768px) {
      .gallery-modal {
        padding: 10px;
      }

      .modal-content {
        max-width: 100%;
        max-height: 100%;
        border-radius: 15px;
      }

      .modal-image {
        height: 60vh;
      }

      .modal-info {
        padding: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .modal-info > div:last-child {
        flex-direction: column;
        width: 100%;
      }

      .modal-info > div:last-child button {
        width: 100%;
        margin-bottom: 10px;
      }

      .artwork-details h3 {
        font-size: 1.25rem;
        margin-bottom: 8px;
      }

      .artwork-price {
        font-size: 1.1rem;
      }

      .buy-button {
        width: 100%;
        justify-content: center;
        padding: 15px 20px;
      }

      .close-modal {
        top: 10px;
        right: 10px;
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
      }

      .nav-arrows {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
      }

      .prev-arrow {
        left: 10px;
      }

      .next-arrow {
        right: 10px;
      }
    }

    @media (max-width: 480px) {
      .modal-image {
        height: 50vh;
      }

      .modal-info {
        padding: 12px;
      }

      .artwork-details h3 {
        font-size: 1.1rem;
      }

      .artwork-price {
        font-size: 1rem;
      }

      .buy-button {
        padding: 12px 16px;
        font-size: 0.9rem;
      }
    }

    /* subtle glass for mobile dropdown overlay */
    .mobile-overlay{ position:fixed; inset:0; background:rgba(14,13,11,0.25); backdrop-filter: blur(3px); z-index:48; display:none; }
    .mobile-overlay.show{ display:block; }
  </style>
</head>
<body>

  <!-- NAVBAR (centered title + dropdown) -->
  <header class="fixed top-0 left-0 right-0 z-50">
    <div class="nav-inner">
      <div id="menu-toggle" class="nav-title text-charcoal">
        <span>Lumora</span>
        <svg id="arrowIcon" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-[--charcoal]" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="transform:rotate(0deg); transition: transform .28s ease">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>

      <!-- Desktop links (hidden visually on small; kept for accessibility) -->
      <nav class="hidden md:flex absolute right-6 top-1/2 transform -translate-y-1/2 gap-6 text-sm font-medium">
        <a href="#hero" class="hover:text-[--muted-gold] transition">Home</a>
        <a href="#gallery" class="hover:text-[--muted-gold] transition">Gallery</a>
        <a href="#about" class="hover:text-[--muted-gold] transition">About</a>
        <a href="#event" class="hover:text-[--muted-gold] transition">Details</a>
      </nav>
    </div>

    <!-- Dropdown / Mobile menu (centered under title) -->
    <div id="dropdown-menu" aria-hidden="true">
      <div class="flex flex-col text-sm">
        <a href="#hero" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Home</a>
        <a href="#gallery" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Gallery</a>
        <a href="#about" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>About</a>
        <a href="#event" class="block px-4 py-3 hover:bg-[--warm-beige] hover:text-white transition" data-link>Details</a>
      </div>
    </div>
  </header>

  <!-- Mobile overlay (dark background when menu open on mobile) -->
  <div id="mobileOverlay" class="mobile-overlay"></div>

  <!-- HERO (full-screen creative) -->
  <main>
    <section id="hero" class="hero-bg min-h-screen flex flex-col justify-center items-center text-center relative pt-16 sm:pt-20">
      <!-- Enhanced decorative shapes with parallax - Art Gallery themed -->
      <img src="https://cdn-icons-png.flaticon.com/512/3659/3659898.png" class="shape shape-1" data-speed="3" alt="paint palette" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2970/2970347.png" class="shape shape-2" data-speed="-2" alt="picture frame" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/3659/3659909.png" class="shape shape-3" data-speed="1.5" alt="paint brush" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2970/2970406.png" class="shape shape-4" data-speed="-1" alt="art canvas" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/3659/3659885.png" class="shape shape-5" data-speed="2" alt="color tube" loading="lazy">
      <img src="https://cdn-icons-png.flaticon.com/512/2970/2970354.png" class="shape shape-6" data-speed="-1.5" alt="easel" loading="lazy">

      <!-- Main hero content -->
      <div class="px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-6xl mx-auto">
        <!-- Enhanced title with gradient and animation -->
        <h1 class="hero-title text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold mb-4 sm:mb-6 opacity-0 leading-tight" data-aos="fade-up">
          The Art of Expression
        </h1>

        <!-- Enhanced subtitle with typewriter effect -->
        <div class="hero-subtitle max-w-4xl mx-auto mb-6 sm:mb-8" data-aos="fade-up" data-aos-delay="300">
          <p class="typewriter text-base sm:text-lg md:text-xl lg:text-2xl text-gray-700 font-light leading-relaxed px-2 sm:px-4">
            Discover a curated collection of contemporary paintings — where color, form and story meet in perfect harmony.
          </p>
        </div>

        <!-- Enhanced call-to-action buttons -->
        <div class="mt-8 sm:mt-12 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 px-4" data-aos="zoom-in" data-aos-delay="800">
          <a href="#gallery"
             class="hero-btn-primary w-full sm:w-auto inline-flex items-center justify-center gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-full text-white text-base sm:text-lg font-semibold
                    shadow-2xl transform transition hover:scale-105 hover:shadow-3xl relative overflow-hidden min-w-0">
            <i class="fa-solid fa-palette text-lg sm:text-xl flex-shrink-0"></i>
            <span class="truncate">Explore Exhibition</span>
            <i class="fa-solid fa-arrow-right ml-1 sm:ml-2 transition-transform group-hover:translate-x-1 flex-shrink-0"></i>
          </a>

          <a href="#about"
             class="hero-btn-secondary w-full sm:w-auto inline-flex items-center justify-center gap-3 px-6 sm:px-8 py-3 sm:py-4 rounded-full text-gray-700 text-base sm:text-lg font-medium
                    transition-all duration-300 hover:text-white min-w-0">
            <i class="fa-solid fa-info-circle text-lg sm:text-xl flex-shrink-0"></i>
            <span class="truncate">Learn More</span>
          </a>
        </div>

        <!-- Additional decorative elements - hidden on small mobile -->
        <div class="mt-12 sm:mt-16 hidden xs:flex justify-center items-center gap-4 sm:gap-8 opacity-60" data-aos="fade-up" data-aos-delay="1200">
          <div class="w-8 sm:w-16 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
          <div class="flex gap-2 sm:gap-3">
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 animate-pulse"></div>
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-orange-400 to-red-500 animate-pulse" style="animation-delay: 0.5s;"></div>
            <div class="w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-gradient-to-r from-red-400 to-pink-500 animate-pulse" style="animation-delay: 1s;"></div>
          </div>
          <div class="w-8 sm:w-16 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent"></div>
        </div>

       
   
    </section>

    <!-- GALLERY -->
   <section id="gallery" class="py-16 px-4 max-w-6xl mx-auto" data-aos="fade-up">
  <h2 class="text-3xl md:text-4xl font-playfair text-center mb-10">Featured Artworks</h2>
  
  <div id="galleryContainer" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8"></div>
</section>

<style>
  .artwork-card {
    border: 6px solid #f5f0e6; /* إطار اللوحة */
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
    position: relative;
    cursor: pointer;
  }

  .artwork-card img {
    display: block;
    width: 100%;
    height: 320px; /* ارتفاع ثابت */
    object-fit: cover; /* اقتصاص جميل */
    transition: transform 0.3s ease;
  }

  .artwork-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 40px rgba(0,0,0,0.25);
  }

  .artwork-card:hover img {
    transform: scale(1.05);
  }

  .artwork-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: white;
    padding: 20px 15px 15px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .artwork-card:hover .artwork-info {
    transform: translateY(0);
  }

  .artwork-title {
    font-family: "Playfair Display", serif;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
  }

  .artwork-price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #ffd700;
  }

  .price-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    color: #8b4513;
    font-size: 0.9rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .artwork-card {
      border-width: 4px;
    }

    .artwork-card img {
      height: 250px;
    }

    .artwork-info {
      padding: 15px 12px 12px;
    }

    .artwork-title {
      font-size: 1rem;
    }

    .artwork-price {
      font-size: 1.1rem;
    }

    .price-tag {
      top: 10px;
      right: 10px;
      padding: 6px 12px;
      font-size: 0.85rem;
    }
  }
</style>



    <!-- ABOUT -->
    <section id="about" class="py-16 bg-[--warm-beige]">
      <div class="max-w-6xl mx-auto grid md:grid-cols-2 gap-8 items-center px-6">
        <div data-aos="fade-right">
          <h2 class="text-3xl font-playfair mb-4">About the Exhibition</h2>
          <p class="text-gray-700 leading-relaxed">Lumora brings together contemporary voices in painting — from experimental techniques to refined compositions. This exhibition explores identity, memory and the language of color.</p>
        </div>
        <div class="flex justify-center" data-aos="fade-left">
          <img src="https://picsum.photos/600/420?random=11" alt="illustration" class="rounded-xl shadow-xl floaty w-full max-w-sm object-cover">
        </div>
      </div>
    </section>

    <!-- EVENT DETAILS -->
    <section id="event" class="py-16">
      <div class="max-w-6xl mx-auto px-6">
        <h2 class="text-3xl font-playfair text-center mb-8" data-aos="fade-up">Event Details</h2>
        <div class="grid gap-6 md:grid-cols-3">
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="80">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-regular fa-calendar-days"></i></div>
            <h3 class="font-semibold mb-1">Date</h3>
            <p class="text-gray-600">March 15 — April 20, 2025</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="160">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-location-dot"></i></div>
            <h3 class="font-semibold mb-1">Location</h3>
            <p class="text-gray-600">Lumora Art Hall, Downtown</p>
          </div>
          <div class="p-6 bg-white rounded-xl shadow-md text-center" data-aos="fade-up" data-aos-delay="240">
            <div class="text-3xl text-[--muted-gold] mb-3"><i class="fa-solid fa-ticket-simple"></i></div>
            <h3 class="font-semibold mb-1">Tickets</h3>
            <p class="text-gray-600">Available Online & Onsite</p>
          </div>
        </div>
      </div>
    </section>

    <!-- GALLERY MODAL -->
    <div id="galleryModal" class="gallery-modal">
      <div class="modal-content">
        <button class="close-modal" id="closeModal">
          <i class="fa-solid fa-times"></i>
        </button>

        <button class="nav-arrows prev-arrow" id="prevArtwork">
          <i class="fa-solid fa-chevron-left"></i>
        </button>

        <button class="nav-arrows next-arrow" id="nextArtwork">
          <i class="fa-solid fa-chevron-right"></i>
        </button>

        <img id="modalImage" class="modal-image" src="" alt="">

        <div class="modal-info">
          <div class="artwork-details">
            <h3 id="modalTitle">Artwork Title</h3>
            <div id="modalPrice" class="artwork-price">75,000 ج.م</div>
          </div>
          <button id="whatsappButton" class="buy-button" style="background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);">
            <i class="fa-brands fa-whatsapp"></i>
            <span>اطلب عبر الواتساب</span>
          </button>
        </div>
      </div>
    </div>



    <!-- FOOTER -->
    <footer class="bg-[--charcoal] text-white py-8">
      <div class="max-w-6xl mx-auto px-6 flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <div class="text-lg font-playfair">Lumora Art Gallery</div>
          <div class="text-sm opacity-80">© 2025 Lumora — All rights reserved</div>
        </div>
        <div class="signature">Alhassan</div>
        <div class="flex gap-4">
          <a href="https://www.instagram.com/l_u_mora?utm_source=qr&igsh=MWE5cGE5YmdiZDh0Yw%3D%3D" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-instagram"></i></a>
          <a href="https://www.facebook.com/share/19UF2UeFdX/" class="p-2 rounded hover:bg-white/10 transition"><i class="fa-brands fa-facebook"></i></a>
         
        </div>
      </div>
    </footer>
  </main>

  <!-- Scripts -->
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>

  <script>
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // init AOS with mobile-optimized settings
    AOS.init({
      duration: isMobile ? 600 : 900,
      once: true,
      easing: 'ease-out-cubic',
      offset: isMobile ? 50 : 120,
      disable: function() {
        return window.innerWidth < 480; // Disable on very small screens
      }
    });

    // Dropdown toggle (centered title)
    const menuToggle = document.getElementById('menu-toggle');
    const dropdownMenu = document.getElementById('dropdown-menu');
    const arrowIcon = document.getElementById('arrowIcon');
    const mobileOverlay = document.getElementById('mobileOverlay');

    let open = false;
    function openMenu() {
      dropdownMenu.classList.add('open');
      arrowIcon.style.transform = 'rotate(180deg)';
      mobileOverlay.classList.add('show');
      open = true;
    }
    function closeMenu() {
      dropdownMenu.classList.remove('open');
      arrowIcon.style.transform = 'rotate(0deg)';
      mobileOverlay.classList.remove('show');
      open = false;
    }

    menuToggle.addEventListener('click', (e) => {
      e.stopPropagation();
      open ? closeMenu() : openMenu();
    });

    // close when clicking outside
    document.addEventListener('click', (e) => {
      if (!dropdownMenu.contains(e.target) && !menuToggle.contains(e.target)) closeMenu();
    });

    // links inside dropdown close menu & smooth highlight
    document.querySelectorAll('[data-link]').forEach(link => {
      link.addEventListener('click', (ev) => {
        ev.preventDefault();
        const targetId = link.getAttribute('href') || link.getAttribute('data-href') || '#hero';
        closeMenu();
        // smooth scroll
        const el = document.querySelector(targetId);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start' });
          // add visual highlight
          el.classList.add('section-highlight');
          setTimeout(()=> el.classList.remove('section-highlight'), 1200);
        }
      });
    });

    // Enhanced parallax shapes with mobile optimization
    let mouseX = 0, mouseY = 0;
    let targetX = 0, targetY = 0;
    let isAnimating = false;

    // Only enable parallax on desktop
    if (!isMobile && !isTouch) {
      document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
      });

      // Smooth parallax animation
      function animateParallax() {
        if (!isAnimating) return;

        targetX += (mouseX - targetX) * 0.05;
        targetY += (mouseY - targetY) * 0.05;

        document.querySelectorAll('.shape').forEach(shape => {
          const speed = parseFloat(shape.dataset.speed) || 1;
          const x = (targetX - window.innerWidth / 2) * speed * 0.01;
          const y = (targetY - window.innerHeight / 2) * speed * 0.01;

          const currentTransform = shape.style.transform;
          const scaleMatch = currentTransform.match(/scale\([^)]*\)/);
          const scaleTransform = scaleMatch ? scaleMatch[0] : '';

          shape.style.transform = `translateX(${x}px) translateY(${y}px) ${scaleTransform}`;
        });

        requestAnimationFrame(animateParallax);
      }

      // Start parallax animation only on desktop
      isAnimating = true;
      animateParallax();

      // Add cursor trail effect for hero section (desktop only)
      const heroSection = document.getElementById('hero');
      let trail = [];
      let lastTrailTime = 0;

      heroSection.addEventListener('mousemove', (e) => {
        const now = Date.now();
        if (now - lastTrailTime < 50) return; // Throttle trail creation
        lastTrailTime = now;

        const rect = heroSection.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Create trail dot
        const dot = document.createElement('div');
        dot.style.cssText = `
          position: absolute;
          left: ${x}px;
          top: ${y}px;
          width: 4px;
          height: 4px;
          background: radial-gradient(circle, rgba(212,163,115,0.6) 0%, transparent 70%);
          border-radius: 50%;
          pointer-events: none;
          z-index: 5;
          animation: trailFade 1s ease-out forwards;
        `;

        heroSection.appendChild(dot);
        trail.push(dot);

        // Remove old trail dots
        if (trail.length > 15) {
          const oldDot = trail.shift();
          if (oldDot && oldDot.parentNode) {
            oldDot.parentNode.removeChild(oldDot);
          }
        }

        // Remove dot after animation
        setTimeout(() => {
          if (dot && dot.parentNode) {
            dot.parentNode.removeChild(dot);
          }
        }, 1000);
      });

      // Add trail fade animation
      const style = document.createElement('style');
      style.textContent = `
        @keyframes trailFade {
          0% { opacity: 1; transform: scale(1); }
          100% { opacity: 0; transform: scale(0.3); }
        }
      `;
      document.head.appendChild(style);
    }

    // Mobile-specific optimizations
    if (isMobile || isTouch) {
      // Disable expensive animations on mobile
      document.querySelectorAll('.shape').forEach(shape => {
        shape.style.willChange = 'auto';
      });

      // Add touch feedback for buttons
      document.querySelectorAll('.hero-btn-primary, .hero-btn-secondary').forEach(btn => {
        btn.addEventListener('touchstart', function() {
          this.style.transform = 'scale(0.95)';
        });

        btn.addEventListener('touchend', function() {
          this.style.transform = 'scale(1)';
        });
      });
    }

    // Enhanced hero animations with mobile optimization
    window.addEventListener('load', () => {
      const title = document.querySelector('.hero-title');
      const typewriter = document.querySelector('.typewriter');

      if (title) {
        setTimeout(() => {
          title.classList.add('animated');
          title.style.opacity = '1';
        }, isMobile ? 150 : 300);
      }

      // Start typewriter effect after title animation (faster on mobile)
      if (typewriter) {
        const typewriterDelay = isMobile ? 800 : 1500;
        const typewriterDuration = isMobile ? 2 : 3;
        const steps = isMobile ? 40 : 60;

        setTimeout(() => {
          typewriter.style.animation = `typewriter ${typewriterDuration}s steps(${steps}) forwards, blink 1s infinite ${typewriterDuration}s`;
        }, typewriterDelay);
      }

      // Add floating animation to shapes on hover (desktop only)
      if (!isMobile && !isTouch) {
        document.querySelectorAll('.shape').forEach(shape => {
          shape.addEventListener('mouseenter', () => {
            shape.style.transform += ' scale(1.1)';
            shape.style.opacity = '0.4';
          });

          shape.addEventListener('mouseleave', () => {
            shape.style.transform = shape.style.transform.replace(' scale(1.1)', '');
            shape.style.opacity = shape.classList.contains('shape-1') ? '0.25' :
                                  shape.classList.contains('shape-2') ? '0.2' :
                                  shape.classList.contains('shape-3') ? '0.15' :
                                  shape.classList.contains('shape-4') ? '0.18' :
                                  shape.classList.contains('shape-5') ? '0.16' : '0.22';
          });
        });
      }
    });

    // Smooth anchor links for desktop nav
    document.querySelectorAll('a[href^="#"]').forEach(a=>{
      a.addEventListener('click', function(ev){
        const href = this.getAttribute('href');
        if (!href || href === '#') return;
        const dest = document.querySelector(href);
        if (dest) {
          ev.preventDefault();
          dest.scrollIntoView({ behavior:'smooth', block:'start' });
          dest.classList.add('section-highlight');
          setTimeout(()=> dest.classList.remove('section-highlight'),1200);
          closeMenu();
        }
      });
    });

    // Artworks Data
    const artworksData = [
      {
        title: "Whispers of Dawn",
        price: "75,000 ج.م",
        image: "https://picsum.photos/id/1018/900/1200"
      },
      {
        title: "Urban Symphony",
        price: "95,000 ج.م",
        image: "https://picsum.photos/id/1025/900/1200"
      },
      {
        title: "Emotional Layers",
        price: "85,000 ج.م",
        image: "https://picsum.photos/id/1035/900/1200"
      },
      {
        title: "Ocean Dreams",
        price: "55,000 ج.م",
        image: "https://picsum.photos/id/1042/900/1200"
      },
      {
        title: "Minimalist Harmony",
        price: "125,000 ج.م",
        image: "https://picsum.photos/id/1050/900/1200"
      },
      {
        title: "Light Refraction",
        price: "45,000 ج.م",
        image: "https://picsum.photos/id/1062/900/1200"
      }
    ];

    // Create Gallery Cards
    function createGalleryCards() {
      const galleryContainer = document.getElementById('galleryContainer');

      artworksData.forEach((artwork, index) => {
        const card = document.createElement('div');
        card.className = 'artwork-card';
        card.setAttribute('data-aos', 'zoom-in');
        card.setAttribute('data-aos-delay', (index * 80 + 80).toString());

        card.innerHTML = `
          <img loading="lazy" src="${artwork.image}" alt="${artwork.title}">
          <div class="price-tag">${artwork.price}</div>
          <div class="artwork-info">
            <div class="artwork-title">${artwork.title}</div>
            <div class="artwork-price">${artwork.price}</div>
          </div>
        `;

        // Add click event for modal view
        card.addEventListener('click', (e) => {
          // Check if clicked on price tag - redirect to sale page
          if (e.target.classList.contains('price-tag')) {
            const saleUrl = `artwork-sale.html?title=${encodeURIComponent(artwork.title)}&price=${encodeURIComponent(artwork.price)}&image=${encodeURIComponent(artwork.image)}`;
            window.open(saleUrl, '_blank');
          } else {
            // Open modal for viewing
            currentArtworkIndex = index;
            openModal(artwork);
          }
        });

        galleryContainer.appendChild(card);
      });
    }

    // Initialize gallery
    createGalleryCards();

    // Gallery Modal Functionality
    const galleryModal = document.getElementById('galleryModal');
    const closeModal = document.getElementById('closeModal');
    const prevArtwork = document.getElementById('prevArtwork');
    const nextArtwork = document.getElementById('nextArtwork');

    let currentArtworkIndex = 0;

    function openModal(artwork) {
      // Add loading state
      const modalImage = document.getElementById('modalImage');
      modalImage.style.opacity = '0.3';

      // Set image source and alt text
      modalImage.src = artwork.image;
      modalImage.alt = artwork.title;

      // Set artwork details
      document.getElementById('modalTitle').textContent = artwork.title;
      document.getElementById('modalPrice').textContent = artwork.price || '75,000 ج.م';

      // Handle image loading
      modalImage.onload = () => {
        modalImage.style.opacity = '1';
      };

      // Show modal
      galleryModal.classList.add('active');
      document.body.style.overflow = 'hidden';
    }

    function closeModalFunc() {
      galleryModal.classList.remove('active');
      document.body.style.overflow = 'auto';
    }

    function showPrevArtwork() {
      currentArtworkIndex = (currentArtworkIndex - 1 + artworksData.length) % artworksData.length;
      openModal(artworksData[currentArtworkIndex]);
    }

    function showNextArtwork() {
      currentArtworkIndex = (currentArtworkIndex + 1) % artworksData.length;
      openModal(artworksData[currentArtworkIndex]);
    }

    // WhatsApp functionality
    const whatsappButton = document.getElementById('whatsappButton');

    function openWhatsApp(artwork) {
      const phoneNumber = "201234567890"; // رقم الواتساب (يجب تغييره للرقم الحقيقي)
      const message = `مرحباً، أريد الاستفسار عن اللوحة:\n\n📸 ${artwork.title}\n💰 السعر: ${artwork.price}\n\nهل يمكنني الحصول على مزيد من التفاصيل؟`;
      const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

      window.open(whatsappUrl, '_blank');
    }

    // Event listeners
    closeModal.addEventListener('click', closeModalFunc);
    prevArtwork.addEventListener('click', showPrevArtwork);
    nextArtwork.addEventListener('click', showNextArtwork);

    buyButton.addEventListener('click', () => {
      const currentArtwork = artworksData[currentArtworkIndex];
      openPurchaseModal(currentArtwork);
    });

    viewDetailsButton.addEventListener('click', () => {
      const currentArtwork = artworksData[currentArtworkIndex];
      const saleUrl = `artwork-sale.html?title=${encodeURIComponent(currentArtwork.title)}&price=${encodeURIComponent(currentArtwork.price)}&image=${encodeURIComponent(currentArtwork.image)}`;
      window.open(saleUrl, '_blank');
    });

    closePurchaseModal.addEventListener('click', closePurchaseModalFunc);
    cancelPurchase.addEventListener('click', closePurchaseModalFunc);

    purchaseForm.addEventListener('submit', (e) => {
      e.preventDefault();

      // Simulate purchase process
      alert(`Thank you for your purchase of "${currentArtworkForPurchase.title}"! We will contact you soon with payment and shipping details.`);
      closePurchaseModalFunc();
    });

    // Close modal when clicking outside
    galleryModal.addEventListener('click', (e) => {
      if (e.target === galleryModal) {
        closeModalFunc();
      }
    });

    purchaseModal.addEventListener('click', (e) => {
      if (e.target === purchaseModal) {
        closePurchaseModalFunc();
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (purchaseModal.classList.contains('active')) {
        if (e.key === 'Escape') {
          closePurchaseModalFunc();
        }
      } else if (galleryModal.classList.contains('active')) {
        switch(e.key) {
          case 'Escape':
            closeModalFunc();
            break;
          case 'ArrowLeft':
            showPrevArtwork();
            break;
          case 'ArrowRight':
            showNextArtwork();
            break;
        }
      } else if (e.key === 'Escape') {
        closeMenu();
      }
    });

    // Accessibility: hide dropdown when resizing up
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (window.innerWidth >= 768) closeMenu();

        // Update mobile detection on resize
        const newIsMobile = window.innerWidth <= 768;
        if (newIsMobile !== isMobile) {
          location.reload(); // Reload to apply mobile optimizations
        }
      }, 250);
    });

    // Performance optimizations for mobile
    if (isMobile || isTouch) {
      // Reduce animation frequency
      document.documentElement.style.setProperty('--animation-duration', '4s');

      // Optimize scroll performance
      let ticking = false;
      function updateScrollEffects() {
        // Minimal scroll effects for mobile
        ticking = false;
      }

      window.addEventListener('scroll', () => {
        if (!ticking) {
          requestAnimationFrame(updateScrollEffects);
          ticking = true;
        }
      }, { passive: true });

      // Prevent zoom on double tap for buttons
      document.querySelectorAll('.hero-btn-primary, .hero-btn-secondary').forEach(btn => {
        btn.addEventListener('touchend', function(e) {
          e.preventDefault();
          this.click();
        });
      });
    }

    // Preload critical images for better performance
    const criticalImages = [
      'https://cdn-icons-png.flaticon.com/512/3659/3659898.png', // paint palette
      'https://cdn-icons-png.flaticon.com/512/2970/2970347.png', // picture frame
      'https://cdn-icons-png.flaticon.com/512/3659/3659909.png', // paint brush
      'https://cdn-icons-png.flaticon.com/512/2970/2970406.png', // art canvas
      'https://cdn-icons-png.flaticon.com/512/3659/3659885.png', // color tube
      'https://cdn-icons-png.flaticon.com/512/2970/2970354.png'  // easel
    ];

    criticalImages.forEach(src => {
      const img = new Image();
      img.src = src;
    });

    // Intersection Observer for performance
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      });

      // Observe lazy-loaded images
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }

    // Add touch gestures for mobile modal navigation
    if (isMobile || isTouch) {
      let startX = 0;
      let startY = 0;

      galleryModal.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
      }, { passive: true });

      galleryModal.addEventListener('touchend', (e) => {
        if (!galleryModal.classList.contains('active')) return;

        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        const diffX = startX - endX;
        const diffY = startY - endY;

        // Horizontal swipe detection
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
          if (diffX > 0) {
            // Swipe left - next artwork
            showNextArtwork();
          } else {
            // Swipe right - previous artwork
            showPrevArtwork();
          }
        }

        // Vertical swipe down to close
        if (diffY < -100 && Math.abs(diffX) < 50) {
          closeModalFunc();
        }
      }, { passive: true });
    }

    // Add animation to artwork cards on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.transform = 'translateY(0)';
          entry.target.style.opacity = '1';
        }
      });
    }, observerOptions);

    // Observe artwork cards
    artworkCards.forEach(card => {
      card.style.transform = 'translateY(20px)';
      card.style.opacity = '0.8';
      card.style.transition = 'transform 0.6s ease, opacity 0.6s ease';
      cardObserver.observe(card);
    });
  </script>
  <script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-app.js";
  import { getFirestore, collection, getDocs } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-firestore.js";

  // إعداد Firebase
  const firebaseConfig = {
    apiKey: "AIzaSyBWgFevCvEx2jsSO_5-7EQVaEed0FjZz0E",
    authDomain: "lumora-admin.firebaseapp.com",
    projectId: "lumora-admin",
    storageBucket: "lumora-admin.appspot.com",
    messagingSenderId: "526329523350",
    appId: "1:526329523350:web:f77c2f9c4b4521129ae6b7",
    measurementId: "G-Z7DQT4M56R"
  };

  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  const galleryContainer = document.getElementById("galleryContainer");

  async function loadGallery() {
    galleryContainer.innerHTML = "";
    const querySnapshot = await getDocs(collection(db, "artworks"));
    querySnapshot.forEach((docSnap) => {
      const data = docSnap.data();

      const card = document.createElement("div");
      card.className = "relative rounded-xl overflow-hidden shadow-lg group cursor-pointer artwork-card";
      card.setAttribute("data-artwork", JSON.stringify({ title: data.title, image: data.image }));

      card.innerHTML = `
        <img loading="lazy" src="${data.image}" alt="${data.title}" class="w-full h-72 object-cover transform transition group-hover:scale-105">
        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition flex items-center justify-center">
          <div class="text-white text-center">
            <i class="fa-solid fa-expand text-3xl mb-2"></i>
            <p class="text-sm font-medium">Click to view</p>
          </div>
        </div>
      `;

      galleryContainer.appendChild(card);
      card.addEventListener("click", () => {
  currentArtworkIndex = Array.from(galleryContainer.children).indexOf(card);
  openModal({ title: data.title, image: data.image });
});

    });
  }

  loadGallery();
</script>

</body>
</html>
