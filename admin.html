<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>لوحة تحكم Lumora — Admin</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <style>
    body { direction: rtl; }
    .hidden { display: none; }
  </style>
</head>
<body class="bg-gray-50 min-h-screen p-6">

  <!-- 📌 فورم تسجيل الدخول -->
  <div id="loginSection" class="max-w-sm mx-auto bg-white p-6 rounded shadow">
    <h1 class="text-xl font-semibold mb-4">تسجيل دخول الأدمن</h1>
    <form id="loginForm" class="space-y-3">
      <input id="loginEmail" type="email" placeholder="البريد الإلكتروني" class="border p-2 w-full" required>
      <input id="loginPassword" type="password" placeholder="كلمة المرور" class="border p-2 w-full" required>
      <button class="bg-blue-600 text-white px-4 py-2 rounded w-full">تسجيل الدخول</button>
    </form>
    <p id="loginError" class="text-red-500 mt-3 text-sm"></p>
  </div>

  <!-- 📌 لوحة التحكم -->
  <div id="adminSection" class="hidden">
    <div class="flex justify-between items-center mb-4">
      <h1 class="text-2xl font-semibold">لوحة تحكم الصور — Lumora</h1>
      <button id="logoutBtn" class="bg-gray-600 text-white px-4 py-2 rounded">تسجيل الخروج</button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow mb-6">
      <form id="addForm" class="space-y-4">
        <input id="title" type="text" placeholder="عنوان الصورة" class="border p-2 w-full" required>
        <input id="imageFile" type="file" accept="image/*" class="border p-2 w-full" required>
        <button id="submitBtn" class="bg-blue-600 text-white px-4 py-2 rounded">إضافة صورة</button>
        <div id="status" class="text-sm text-gray-600"></div>
      </form>
    </div>

    <div id="gallery" class="grid grid-cols-1 sm:grid-cols-2 gap-4"></div>
  </div>

  <script type="module">
    import { initializeApp } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-app.js";
    import { getFirestore, collection, addDoc, getDocs, deleteDoc, doc } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-firestore.js";
    import { getAuth, signInWithEmailAndPassword, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-auth.js";

    // 🔹 إعداد Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyBWgFevCvEx2jsSO_5-7EQVaEed0FjZz0E",
      authDomain: "lumora-admin.firebaseapp.com",
      projectId: "lumora-admin",
      storageBucket: "lumora-admin.appspot.com",
      messagingSenderId: "526329523350",
      appId: "1:526329523350:web:f77c2f9c4b4521129ae6b7",
      measurementId: "G-Z7DQT4M56R"
    };

    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    const auth = getAuth(app);

    // عناصر DOM
    const loginSection = document.getElementById("loginSection");
    const adminSection = document.getElementById("adminSection");
    const loginForm = document.getElementById("loginForm");
    const loginError = document.getElementById("loginError");
    const logoutBtn = document.getElementById("logoutBtn");
    const addForm = document.getElementById("addForm");
    const gallery = document.getElementById("gallery");
    const status = document.getElementById("status");
    const submitBtn = document.getElementById("submitBtn");

    // 🔹 رفع صورة إلى ImgBB
    async function uploadToImgBB(file) {
      const formData = new FormData();
      formData.append("image", file);
      const res = await fetch("https://api.imgbb.com/1/upload?key=e64da77c2a74b1ac1382a8e5456adf25", {
        method: "POST",
        body: formData
      });
      const result = await res.json();
      if (!result.success) throw new Error("رفع الصورة فشل");
      return result.data.url;
    }

    // 🔹 تحميل الصور من Firestore
    async function loadImages() {
      gallery.innerHTML = "";
      const snapshot = await getDocs(collection(db, "artworks"));
      snapshot.forEach(docSnap => {
        const data = docSnap.data();
        const div = document.createElement("div");
        div.className = "bg-white p-3 rounded shadow";
        div.innerHTML = `
          <img src="${data.image}" class="w-full h-48 object-cover rounded mb-2">
          <h3 class="font-semibold">${data.title}</h3>
          <button data-id="${docSnap.id}" class="delete-btn bg-red-500 text-white px-3 py-1 rounded mt-2">حذف</button>
        `;
        gallery.appendChild(div);
      });

      // حذف الصورة
      document.querySelectorAll(".delete-btn").forEach(btn => {
        btn.addEventListener("click", async () => {
          await deleteDoc(doc(db, "artworks", btn.dataset.id));
          loadImages();
        });
      });
    }

    // 🔹 تسجيل الدخول
    loginForm.addEventListener("submit", async (e) => {
      e.preventDefault();
      const email = document.getElementById("loginEmail").value;
      const password = document.getElementById("loginPassword").value;
      try {
        await signInWithEmailAndPassword(auth, email, password);
      } catch (err) {
        loginError.textContent = "خطأ في تسجيل الدخول";
      }
    });

    // 🔹 تسجيل الخروج
    logoutBtn.addEventListener("click", async () => {
      await signOut(auth);
    });

    // 🔹 إضافة صورة جديدة
    addForm.addEventListener("submit", async (e) => {
      e.preventDefault();
      const title = document.getElementById("title").value;
      const file = document.getElementById("imageFile").files[0];
      if (!file) return;
      status.textContent = "جاري الرفع...";
      try {
        const imageUrl = await uploadToImgBB(file);
        await addDoc(collection(db, "artworks"), { title, image: imageUrl });
        addForm.reset();
        loadImages();
        status.textContent = "تمت الإضافة بنجاح";
      } catch (err) {
        status.textContent = "فشل رفع الصورة";
      }
    });

    // 🔹 متابعة حالة تسجيل الدخول
    onAuthStateChanged(auth, (user) => {
      if (user) {
        loginSection.classList.add("hidden");
        adminSection.classList.remove("hidden");
        loadImages();
      } else {
        loginSection.classList.remove("hidden");
        adminSection.classList.add("hidden");
      }
    });
  </script>
</body>
</html>
