<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=5,user-scalable=yes,viewport-fit=cover" />
  <title>Artwork Sale — Lumora Gallery</title>
  <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/2913/2913461.png">

  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">

  <!-- Font Awesome for icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

  <style>
    :root{
      --beige:#faf5ef;
      --warm-beige:#f7ede2;
      --muted-gold:#d4a373;
      --charcoal:#3e2c2c;
    }

    html { scroll-behavior: smooth; }
    body { background: var(--beige); color: var(--charcoal); font-family: "Inter", system-ui, sans-serif; }

    .artwork-image {
      border: 8px solid #f5f0e6;
      border-radius: 20px;
      box-shadow: 0 12px 30px rgba(0,0,0,0.15);
    }

    .price-highlight {
      background: linear-gradient(135deg, #d4a373 0%, #c19a6b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .buy-btn {
      background: linear-gradient(135deg, #d4a373 0%, #c19a6b 100%);
      transition: all 0.3s ease;
    }

    .buy-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212, 163, 115, 0.4);
    }

    .feature-card {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(212, 163, 115, 0.2);
    }

    @media (max-width: 768px) {
      .artwork-image {
        border-width: 6px;
        border-radius: 15px;
      }
    }
  </style>
  <!-- 📌 أضف هذا داخل <head> قبل إغلاقه -->
<script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-app.js";
  import { getFirestore, doc, getDoc } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-firestore.js";

  // إعداد Firebase
  const firebaseConfig = {
    apiKey: "AIzaSyBWgFevCvEx2jsSO_5-7EQVaEed0FjZz0E",
    authDomain: "lumora-admin.firebaseapp.com",
    projectId: "lumora-admin",
    storageBucket: "lumora-admin.appspot.com",
    messagingSenderId: "526329523350",
    appId: "1:526329523350:web:f77c2f9c4b4521129ae6b7",
    measurementId: "G-Z7DQT4M56R"
  };

  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  // جلب معرف اللوحة من الرابط
  const params = new URLSearchParams(window.location.search);
  const artworkId = params.get("id");

  // عناصر HTML اللي هنعدلها
  const titleEl = document.getElementById("artworkTitle");
  const priceEl = document.getElementById("artworkPrice");
  const mainImgEl = document.getElementById("mainArtworkImage");

  async function loadArtwork() {
    if (!artworkId) {
      titleEl.textContent = "خطأ: لم يتم تحديد اللوحة";
      return;
    }

    try {
      const docRef = doc(db, "artworks", artworkId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data();
        titleEl.textContent = data.title || "بدون عنوان";
        priceEl.textContent = data.price || "غير محدد";
        mainImgEl.src = data.image || "https://via.placeholder.com/800x1000";
      } else {
        titleEl.textContent = "اللوحة غير موجودة";
      }
    } catch (err) {
      console.error(err);
      titleEl.textContent = "حدث خطأ أثناء جلب البيانات";
    }
  }

  loadArtwork();
</script>

</head>
<body>

  <!-- HEADER -->
  <header class="bg-white/80 backdrop-blur-md shadow-sm sticky top-0 z-50">
    <div class="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
      <div class="flex items-center gap-3">
        <a href="index.html" class="text-2xl font-bold text-[--charcoal] font-playfair">
          <i class="fa-solid fa-arrow-left mr-2"></i>
          Lumora
        </a>
      </div>
      <div class="text-sm text-gray-600">
        <i class="fa-solid fa-palette mr-1"></i>
        Art Gallery
      </div>
    </div>
  </header>

  <!-- MAIN CONTENT -->
  <main class="max-w-6xl mx-auto px-4 py-8">
    
    <!-- ARTWORK SHOWCASE -->
    <section class="grid md:grid-cols-2 gap-12 items-start mb-16">
      
      <!-- Artwork Image -->
      <div class="space-y-6">
        <div class="relative">
          <img id="mainArtworkImage" src="https://picsum.photos/id/1018/800/1000" alt="Artwork" 
               class="artwork-image w-full object-cover">
          <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full">
            <span class="font-bold text-[--charcoal]">Original</span>
          </div>
        </div>
        
        <!-- Thumbnail Gallery -->
        <div class="grid grid-cols-4 gap-3">
          <img src="https://picsum.photos/id/1018/200/200" alt="View 1" 
               class="w-full h-20 object-cover rounded-lg border-2 border-[--warm-beige] cursor-pointer hover:border-[--muted-gold] transition">
          <img src="https://picsum.photos/id/1019/200/200" alt="View 2" 
               class="w-full h-20 object-cover rounded-lg border-2 border-[--warm-beige] cursor-pointer hover:border-[--muted-gold] transition">
          <img src="https://picsum.photos/id/1020/200/200" alt="View 3" 
               class="w-full h-20 object-cover rounded-lg border-2 border-[--warm-beige] cursor-pointer hover:border-[--muted-gold] transition">
          <img src="https://picsum.photos/id/1021/200/200" alt="Detail" 
               class="w-full h-20 object-cover rounded-lg border-2 border-[--warm-beige] cursor-pointer hover:border-[--muted-gold] transition">
        </div>
      </div>

      <!-- Artwork Details -->
      <div class="space-y-8">
        <div>
          <h1 id="artworkTitle" class="text-4xl md:text-5xl font-bold font-playfair text-[--charcoal] mb-4">
            Whispers of Dawn
          </h1>
          <p class="text-xl text-gray-600 mb-6">by Elena Rodriguez • 2024</p>
          
          <div id="artworkPrice" class="text-5xl font-bold price-highlight mb-8">
            $2,400
          </div>
          
          <p class="text-lg text-gray-700 leading-relaxed mb-8">
            A mesmerizing exploration of light and shadow, capturing the ethereal beauty of early morning mist over a tranquil landscape. The artist masterfully blends warm and cool tones to create a sense of peaceful awakening.
          </p>
        </div>

        <!-- Purchase Section -->
        <div class="space-y-6">
          <button id="buyNowBtn" class="buy-btn w-full py-4 px-8 text-white text-xl font-semibold rounded-full">
            <i class="fa-solid fa-shopping-cart mr-3"></i>
            Buy Now
          </button>
          
          <div class="grid grid-cols-2 gap-4">
            <button class="border-2 border-[--muted-gold] text-[--muted-gold] py-3 px-6 rounded-full font-semibold hover:bg-[--muted-gold] hover:text-white transition">
              <i class="fa-solid fa-heart mr-2"></i>
              Add to Wishlist
            </button>
            <button class="border-2 border-gray-300 text-gray-600 py-3 px-6 rounded-full font-semibold hover:bg-gray-100 transition">
              <i class="fa-solid fa-share mr-2"></i>
              Share
            </button>
          </div>
        </div>

        <!-- Artwork Specifications -->
        <div class="feature-card p-6 rounded-xl">
          <h3 class="text-xl font-semibold mb-4">Artwork Details</h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-500">Medium:</span>
              <span class="font-semibold ml-2">Oil on Canvas</span>
            </div>
            <div>
              <span class="text-gray-500">Dimensions:</span>
              <span class="font-semibold ml-2">80 × 60 cm</span>
            </div>
            <div>
              <span class="text-gray-500">Year:</span>
              <span class="font-semibold ml-2">2024</span>
            </div>
            <div>
              <span class="text-gray-500">Style:</span>
              <span class="font-semibold ml-2">Contemporary</span>
            </div>
            <div>
              <span class="text-gray-500">Framing:</span>
              <span class="font-semibold ml-2">Included</span>
            </div>
            <div>
              <span class="text-gray-500">Certificate:</span>
              <span class="font-semibold ml-2">Authenticity</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FEATURES -->
    <section class="grid md:grid-cols-3 gap-8 mb-16">
      <div class="feature-card p-6 rounded-xl text-center">
        <div class="text-3xl text-[--muted-gold] mb-4">
          <i class="fa-solid fa-shipping-fast"></i>
        </div>
        <h3 class="text-lg font-semibold mb-2">Free Shipping</h3>
        <p class="text-gray-600">Worldwide delivery with professional packaging</p>
      </div>
      
      <div class="feature-card p-6 rounded-xl text-center">
        <div class="text-3xl text-[--muted-gold] mb-4">
          <i class="fa-solid fa-certificate"></i>
        </div>
        <h3 class="text-lg font-semibold mb-2">Authenticity</h3>
        <p class="text-gray-600">Certificate of authenticity included</p>
      </div>
      
      <div class="feature-card p-6 rounded-xl text-center">
        <div class="text-3xl text-[--muted-gold] mb-4">
          <i class="fa-solid fa-undo"></i>
        </div>
        <h3 class="text-lg font-semibold mb-2">30-Day Return</h3>
        <p class="text-gray-600">Money-back guarantee if not satisfied</p>
      </div>
    </section>

  </main>

  <!-- PURCHASE MODAL -->
  <div id="purchaseModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold font-playfair">Purchase Artwork</h2>
          <button id="closePurchaseModal" class="text-gray-400 hover:text-gray-600">
            <i class="fa-solid fa-times text-xl"></i>
          </button>
        </div>
        
        <div class="flex gap-4 mb-6">
          <img id="purchaseArtworkImage" src="" alt="" class="w-20 h-20 object-cover rounded-lg border-2 border-[--warm-beige]">
          <div>
            <h3 id="purchaseArtworkTitle" class="font-semibold text-lg"></h3>
            <div id="purchaseArtworkPrice" class="text-xl font-bold text-[--muted-gold]"></div>
          </div>
        </div>
        
        <form id="purchaseForm" class="space-y-4">
          <div>
            <label class="block text-sm font-semibold mb-2">Full Name</label>
            <input type="text" required class="w-full p-3 border border-gray-300 rounded-lg focus:border-[--muted-gold] focus:outline-none">
          </div>
          
          <div>
            <label class="block text-sm font-semibold mb-2">Email</label>
            <input type="email" required class="w-full p-3 border border-gray-300 rounded-lg focus:border-[--muted-gold] focus:outline-none">
          </div>
          
          <div>
            <label class="block text-sm font-semibold mb-2">Phone Number</label>
            <input type="tel" required class="w-full p-3 border border-gray-300 rounded-lg focus:border-[--muted-gold] focus:outline-none">
          </div>
          
          <div>
            <label class="block text-sm font-semibold mb-2">Shipping Address</label>
            <textarea required rows="3" class="w-full p-3 border border-gray-300 rounded-lg focus:border-[--muted-gold] focus:outline-none resize-none"></textarea>
          </div>
          
          <div class="flex gap-3 pt-4">
            <button type="button" id="cancelPurchase" class="flex-1 py-3 border border-gray-300 text-gray-600 rounded-lg font-semibold hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" class="flex-2 py-3 bg-gradient-to-r from-[--muted-gold] to-[#c19a6b] text-white rounded-lg font-semibold hover:shadow-lg">
              Confirm Purchase
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    // Get artwork data from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const artworkData = {
      title: urlParams.get('title') || 'Whispers of Dawn',
      price: urlParams.get('price') || '$2,400',
      image: urlParams.get('image') || 'https://picsum.photos/id/1018/800/1000'
    };

    // Update page content
    document.getElementById('artworkTitle').textContent = artworkData.title;
    document.getElementById('artworkPrice').textContent = artworkData.price;
    document.getElementById('mainArtworkImage').src = artworkData.image;

    // Purchase functionality
    const purchaseModal = document.getElementById('purchaseModal');
    const buyNowBtn = document.getElementById('buyNowBtn');
    const closePurchaseModal = document.getElementById('closePurchaseModal');
    const cancelPurchase = document.getElementById('cancelPurchase');
    const purchaseForm = document.getElementById('purchaseForm');

    function openPurchaseModal() {
      document.getElementById('purchaseArtworkImage').src = artworkData.image;
      document.getElementById('purchaseArtworkTitle').textContent = artworkData.title;
      document.getElementById('purchaseArtworkPrice').textContent = artworkData.price;
      
      purchaseModal.classList.remove('hidden');
      purchaseModal.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }

    function closePurchaseModalFunc() {
      purchaseModal.classList.add('hidden');
      purchaseModal.classList.remove('flex');
      document.body.style.overflow = 'auto';
      purchaseForm.reset();
    }

    // Event listeners
    buyNowBtn.addEventListener('click', openPurchaseModal);
    closePurchaseModal.addEventListener('click', closePurchaseModalFunc);
    cancelPurchase.addEventListener('click', closePurchaseModalFunc);

    purchaseForm.addEventListener('submit', (e) => {
      e.preventDefault();
      alert(`Thank you for your purchase of "${artworkData.title}"! We will contact you soon with payment and shipping details.`);
      closePurchaseModalFunc();
    });

    // Close modal when clicking outside
    purchaseModal.addEventListener('click', (e) => {
      if (e.target === purchaseModal) {
        closePurchaseModalFunc();
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && !purchaseModal.classList.contains('hidden')) {
        closePurchaseModalFunc();
      }
    });

    // Thumbnail gallery functionality
    document.querySelectorAll('img[alt^="View"], img[alt="Detail"]').forEach(thumb => {
      thumb.addEventListener('click', () => {
        document.getElementById('mainArtworkImage').src = thumb.src.replace('/200/200', '/800/1000');
      });
    });
  </script>

</body>
</html>
